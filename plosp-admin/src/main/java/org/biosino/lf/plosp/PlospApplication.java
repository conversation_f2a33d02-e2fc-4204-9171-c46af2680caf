package org.biosino.lf.plosp;

import org.biosino.lf.plosp.task.config.RabbitMqProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"org.biosino.lf.pds.**", "org.biosino.lf.plosp.**"})
@EnableConfigurationProperties({RabbitMqProperties.class})
public class PlospApplication {
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(PlospApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  PLOSP启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
