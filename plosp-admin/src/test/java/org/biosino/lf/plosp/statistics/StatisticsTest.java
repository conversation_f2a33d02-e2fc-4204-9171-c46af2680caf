package org.biosino.lf.plosp.statistics;

import org.biosino.lf.plosp.task.service.stat.StatisticsArticlePublishedService;
import org.biosino.lf.plosp.task.service.stat.StatisticsArticleService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2025/8/29
 */
@SpringBootTest
public class StatisticsTest {

    @Autowired
    private StatisticsArticleService articleService;

    @Autowired
    private StatisticsArticlePublishedService articlePublishedService;

    @Test
    public void test1() {
        articleService.calculateAll();
    }

    @Test
    public void test2() {
        articlePublishedService.calculateAll();
    }
}
